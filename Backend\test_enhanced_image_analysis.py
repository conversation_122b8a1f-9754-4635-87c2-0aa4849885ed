#!/usr/bin/env python3
"""
Test script for enhanced image analysis functionality.
Tests the new comprehensive analysis features for architecture diagrams and error screenshots.
"""

import os
import sys
import requests
import json
from pathlib import Path

# Add the Backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from gemini_utils import (
    detect_image_type_from_text,
    select_analysis_prompt,
    format_analysis_response,
    ARCHITECTURE_DIAGRAM_PROMPT,
    ERROR_SCREENSHOT_PROMPT,
    GENERAL_ANALYSIS_PROMPT
)

def test_image_type_detection():
    """Test the image type detection functionality."""
    print("Testing image type detection...")
    
    # Test architecture diagram text
    arch_text = "API Gateway connects to microservices running on AWS ECS with RDS database and Redis cache"
    arch_type = detect_image_type_from_text(arch_text)
    print(f"Architecture text detected as: {arch_type}")
    assert arch_type == "architecture", f"Expected 'architecture', got '{arch_type}'"
    
    # Test error screenshot text
    error_text = "Exception in thread main java.lang.NullPointerException at line 42 stack trace error failed"
    error_type = detect_image_type_from_text(error_text)
    print(f"Error text detected as: {error_type}")
    assert error_type == "error", f"Expected 'error', got '{error_type}'"
    
    # Test general text
    general_text = "This is some random text without technical keywords"
    general_type = detect_image_type_from_text(general_text)
    print(f"General text detected as: {general_type}")
    assert general_type == "general", f"Expected 'general', got '{general_type}'"
    
    print("✅ Image type detection tests passed!")

def test_prompt_selection():
    """Test the prompt selection functionality."""
    print("\nTesting prompt selection...")
    
    # Test architecture prompt
    arch_prompt = select_analysis_prompt("architecture")
    assert "COMPONENT ANALYSIS" in arch_prompt
    assert "ARCHITECTURE PATTERNS" in arch_prompt
    print("✅ Architecture prompt selected correctly")
    
    # Test error prompt
    error_prompt = select_analysis_prompt("error")
    assert "ERROR IDENTIFICATION" in error_prompt
    assert "TROUBLESHOOTING STEPS" in error_prompt
    print("✅ Error prompt selected correctly")
    
    # Test custom prompt override
    custom_prompt = "Custom analysis prompt"
    selected_prompt = select_analysis_prompt("architecture", custom_prompt)
    assert selected_prompt == custom_prompt
    print("✅ Custom prompt override works correctly")
    
    print("✅ Prompt selection tests passed!")

def test_response_formatting():
    """Test the response formatting functionality."""
    print("\nTesting response formatting...")
    
    # Test architecture response formatting
    arch_analysis = """
    ## COMPONENT ANALYSIS
    - API Gateway: Routes requests to backend services
    - Microservices: Handle business logic using Node.js
    - Database: PostgreSQL for data persistence
    
    ## ARCHITECTURE PATTERNS & RELATIONSHIPS
    The system follows a microservices architecture pattern with event-driven communication.
    
    ## TECHNICAL ASSESSMENT
    Potential bottlenecks include the single database instance.
    Single points of failure: API Gateway and database.
    
    ## RECOMMENDATIONS & BEST PRACTICES
    - Implement database clustering for high availability
    - Add caching layer with Redis
    """
    
    formatted_arch = format_analysis_response(arch_analysis, "architecture", "API Gateway microservices database")
    
    assert formatted_arch["analysis_type"] == "architecture_diagram"
    assert "components" in formatted_arch
    assert "recommendations" in formatted_arch
    assert len(formatted_arch["components"]) > 0
    print("✅ Architecture response formatting works correctly")
    
    # Test error response formatting
    error_analysis = """
    ## ERROR IDENTIFICATION
    This is a Java NullPointerException occurring in the main thread.
    The error indicates a null reference was accessed.
    
    ## ERROR EXPLANATION
    NullPointerException occurs when code attempts to use a reference that points to no location in memory.
    
    ## TROUBLESHOOTING STEPS
    1. Check the line number mentioned in the stack trace
    2. Verify object initialization before use
    3. Add null checks where appropriate
    
    ## SOLUTION RECOMMENDATIONS
    - Initialize objects properly before use
    - Add defensive null checks
    - Use Optional class for better null handling
    """
    
    formatted_error = format_analysis_response(error_analysis, "error", "NullPointerException java error")
    
    assert formatted_error["analysis_type"] == "error_screenshot"
    assert "error_details" in formatted_error
    assert "troubleshooting_steps" in formatted_error
    assert "solutions" in formatted_error
    print("✅ Error response formatting works correctly")
    
    print("✅ Response formatting tests passed!")

def test_api_endpoint():
    """Test the enhanced API endpoint if server is running."""
    print("\nTesting API endpoint (if server is running)...")
    
    try:
        # Test if server is running
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("⚠️  Server not running, skipping API tests")
            return
    except requests.exceptions.RequestException:
        print("⚠️  Server not running, skipping API tests")
        return
    
    # Create a simple test image (1x1 pixel PNG)
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x0cIDATx\x9cc```\x00\x00\x00\x04\x00\x01\xdd\x8d\xb4\x1c\x00\x00\x00\x00IEND\xaeB`\x82'
    
    # Test enhanced analysis
    files = {'file': ('test.png', test_image_data, 'image/png')}
    data = {'enhanced': 'true'}
    
    try:
        response = requests.post("http://localhost:8000/analyze/image/gemini", files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API endpoint responded successfully")
            print(f"Response keys: {list(result.keys())}")
            
            if "enhanced_analysis" in result:
                print("✅ Enhanced analysis included in response")
                enhanced = result["enhanced_analysis"]
                print(f"Analysis type: {enhanced.get('analysis_type', 'unknown')}")
            else:
                print("⚠️  Enhanced analysis not found in response")
        else:
            print(f"⚠️  API endpoint returned status {response.status_code}")
            print(f"Response: {response.text}")
    
    except requests.exceptions.RequestException as e:
        print(f"⚠️  API request failed: {e}")

def main():
    """Run all tests."""
    print("🧪 Testing Enhanced Image Analysis Functionality")
    print("=" * 50)
    
    try:
        test_image_type_detection()
        test_prompt_selection()
        test_response_formatting()
        test_api_endpoint()
        
        print("\n" + "=" * 50)
        print("🎉 All tests completed successfully!")
        print("\nThe enhanced image analysis system is ready to provide:")
        print("📊 Comprehensive architecture diagram analysis")
        print("🐛 Detailed error screenshot troubleshooting")
        print("📝 Structured, actionable responses")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

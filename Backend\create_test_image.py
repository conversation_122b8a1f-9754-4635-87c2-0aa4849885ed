"""
Create a test image with clear text for OCR testing.
"""
import os
from PIL import Image, ImageDraw, ImageFont

def create_test_image():
    """Create a high-quality test image with clear text."""
    # Create a larger image with white background
    width, height = 800, 400
    img = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to use a better font
    try:
        # Try to use a system font
        font_large = ImageFont.truetype("arial.ttf", 36)
        font_medium = ImageFont.truetype("arial.ttf", 24)
        font_small = ImageFont.truetype("arial.ttf", 18)
    except:
        # Fall back to default font
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Add multiple lines of text
    texts = [
        ("OCR Test Document", 50, 50, font_large),
        ("This is a sample document for testing", 50, 120, font_medium),
        ("optical character recognition functionality.", 50, 160, font_medium),
        ("", 50, 200, font_medium),  # Empty line
        ("Key Features:", 50, 240, font_medium),
        ("• Text extraction from images", 70, 280, font_small),
        ("• Multiple language support", 70, 310, font_small),
        ("• High accuracy recognition", 70, 340, font_small),
    ]
    
    # Draw all text
    for text, x, y, font in texts:
        if text:  # Skip empty lines
            draw.text((x, y), text, fill='black', font=font)
    
    # Add a simple border
    draw.rectangle([(10, 10), (width-10, height-10)], outline='black', width=2)
    
    # Save the image
    output_path = "test_ocr_image.png"
    img.save(output_path, "PNG", quality=95)
    print(f"✅ Created test image: {output_path}")
    
    return output_path

if __name__ == "__main__":
    create_test_image()

"""
Architecture diagram analyzer for interpreting and explaining architecture diagrams.
"""
import logging
from typing import Dict, List, Tuple, Optional, Any, Set
from collections import defaultdict, Counter
import networkx as nx

from .diagram_components import DiagramComponent, DiagramConnection, ComponentType
from .models import AnalysisDetail, Recommendation

# Configure logging
logger = logging.getLogger(__name__)


class ArchitecturePattern:
    """Class representing an architecture pattern detected in a diagram."""
    
    def __init__(self, name: str, confidence: float, components: List[str], description: str):
        """
        Initialize an architecture pattern.
        
        Args:
            name: Name of the pattern
            confidence: Confidence score for the pattern detection
            components: List of component IDs involved in the pattern
            description: Description of the pattern
        """
        self.name = name
        self.confidence = confidence
        self.components = components
        self.description = description
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the pattern to a dictionary."""
        return {
            "name": self.name,
            "confidence": self.confidence,
            "components": self.components,
            "description": self.description
        }


class ArchitectureDiagramAnalyzer:
    """
    Service for analyzing architecture diagrams and identifying patterns.
    
    This service takes diagram components and connections as input and
    performs analysis to identify architecture patterns, component roles,
    and potential issues or recommendations.
    """
    
    # Comprehensive AWS service detection keywords
    AWS_SERVICE_KEYWORDS = {
        # Compute Services
        "ec2": ["ec2", "elastic compute", "virtual machine", "vm", "instance", "server", "compute"],
        "lambda": ["lambda", "function", "serverless", "faas", "aws lambda"],
        "ecs": ["ecs", "elastic container", "container service", "docker", "fargate"],
        "eks": ["eks", "elastic kubernetes", "k8s", "kubernetes"],
        "batch": ["batch", "aws batch", "batch computing"],
        "lightsail": ["lightsail", "vps", "virtual private server"],

        # Storage Services
        "s3": ["s3", "simple storage", "bucket", "object storage", "blob"],
        "ebs": ["ebs", "elastic block", "block storage", "volume"],
        "efs": ["efs", "elastic file", "file system", "nfs"],
        "fsx": ["fsx", "file system", "lustre", "windows file"],
        "glacier": ["glacier", "archive", "cold storage", "backup"],
        "storage_gateway": ["storage gateway", "hybrid storage"],

        # Database Services
        "rds": ["rds", "relational database", "mysql", "postgres", "oracle", "sql server"],
        "dynamodb": ["dynamodb", "nosql", "document database", "key-value"],
        "redshift": ["redshift", "data warehouse", "analytics database"],
        "elasticache": ["elasticache", "redis", "memcached", "cache", "in-memory"],
        "neptune": ["neptune", "graph database", "graph"],
        "documentdb": ["documentdb", "mongodb", "document"],
        "timestream": ["timestream", "time series", "iot database"],

        # Networking Services
        "vpc": ["vpc", "virtual private cloud", "network", "subnet"],
        "cloudfront": ["cloudfront", "cdn", "content delivery", "edge"],
        "route53": ["route53", "dns", "domain name", "routing"],
        "elb": ["elb", "elastic load balancer", "load balancer", "alb", "nlb", "clb"],
        "api_gateway": ["api gateway", "api", "rest api", "graphql"],
        "direct_connect": ["direct connect", "dedicated connection"],
        "transit_gateway": ["transit gateway", "tgw", "network hub"],
        "nat_gateway": ["nat gateway", "nat", "network address translation"],
        "internet_gateway": ["internet gateway", "igw", "internet access"],

        # Security Services
        "iam": ["iam", "identity", "access management", "roles", "policies"],
        "cognito": ["cognito", "user pool", "identity pool", "authentication"],
        "waf": ["waf", "web application firewall", "firewall"],
        "shield": ["shield", "ddos protection", "ddos"],
        "kms": ["kms", "key management", "encryption", "keys"],
        "secrets_manager": ["secrets manager", "secrets", "credentials"],
        "certificate_manager": ["certificate manager", "ssl", "tls", "certificates"],
        "security_hub": ["security hub", "security center"],
        "guardduty": ["guardduty", "threat detection"],

        # Monitoring & Management
        "cloudwatch": ["cloudwatch", "monitoring", "metrics", "logs", "alarms"],
        "cloudtrail": ["cloudtrail", "audit", "api logging", "governance"],
        "config": ["config", "compliance", "configuration"],
        "systems_manager": ["systems manager", "ssm", "parameter store"],
        "cloudformation": ["cloudformation", "infrastructure as code", "iac", "stack"],
        "elastic_beanstalk": ["elastic beanstalk", "platform as a service", "paas"],

        # Analytics & ML
        "kinesis": ["kinesis", "streaming", "real-time", "data stream"],
        "emr": ["emr", "elastic mapreduce", "hadoop", "spark"],
        "glue": ["glue", "etl", "data catalog"],
        "athena": ["athena", "query", "sql", "serverless analytics"],
        "quicksight": ["quicksight", "business intelligence", "bi", "dashboard"],
        "sagemaker": ["sagemaker", "machine learning", "ml", "ai"],

        # Application Integration
        "sqs": ["sqs", "simple queue", "message queue", "queue"],
        "sns": ["sns", "simple notification", "notification", "pub/sub"],
        "eventbridge": ["eventbridge", "event bus", "events"],
        "step_functions": ["step functions", "workflow", "state machine"],

        # Developer Tools
        "codecommit": ["codecommit", "git", "source control"],
        "codebuild": ["codebuild", "build", "ci/cd"],
        "codedeploy": ["codedeploy", "deployment"],
        "codepipeline": ["codepipeline", "pipeline", "ci/cd"]
    }

    # Legacy component keywords for backward compatibility
    COMPONENT_KEYWORDS = {
        "database": ["db", "database", "storage", "sql", "nosql", "postgres", "mysql", "mongodb", "dynamodb", "redis", "cache"],
        "api": ["api", "rest", "graphql", "endpoint", "service", "microservice"],
        "frontend": ["ui", "frontend", "client", "web", "app", "interface", "react", "angular", "vue"],
        "backend": ["backend", "server", "service", "processor", "handler", "controller"],
        "queue": ["queue", "kafka", "rabbitmq", "sqs", "pubsub", "event", "bus", "topic"],
        "auth": ["auth", "authentication", "authorization", "identity", "security", "login", "oauth"],
        "load_balancer": ["load balancer", "elb", "alb", "nlb", "gateway", "router"],
        "cdn": ["cdn", "cloudfront", "content delivery", "edge", "cache"],
        "serverless": ["lambda", "function", "serverless", "faas"],
        "container": ["container", "docker", "kubernetes", "k8s", "pod", "ecs"],
        "storage": ["s3", "blob", "storage", "bucket", "file"],
        "analytics": ["analytics", "metrics", "monitoring", "logging", "dashboard"]
    }
    
    # AWS-specific architecture patterns
    AWS_ARCHITECTURE_PATTERNS = {
        "aws_three_tier": {
            "required_services": ["elb", "ec2", "rds"],
            "optional_services": ["cloudfront", "s3", "elasticache"],
            "description": "AWS three-tier web application with load balancer, compute instances, and managed database"
        },
        "aws_serverless": {
            "required_services": ["api_gateway", "lambda", "dynamodb"],
            "optional_services": ["s3", "cloudfront", "cognito"],
            "description": "AWS serverless architecture using API Gateway, Lambda functions, and DynamoDB"
        },
        "aws_microservices": {
            "required_services": ["ecs", "elb", "rds"],
            "optional_services": ["api_gateway", "elasticache", "sqs"],
            "description": "AWS microservices architecture using containers with ECS and load balancing"
        },
        "aws_data_lake": {
            "required_services": ["s3", "glue", "athena"],
            "optional_services": ["kinesis", "emr", "quicksight"],
            "description": "AWS data lake architecture for big data analytics and processing"
        },
        "aws_high_availability": {
            "required_services": ["elb", "ec2", "rds"],
            "patterns": ["multi_az", "auto_scaling"],
            "description": "AWS high availability architecture with multi-AZ deployment and auto scaling"
        },
        "aws_hybrid_cloud": {
            "required_services": ["direct_connect", "vpc", "storage_gateway"],
            "optional_services": ["ec2", "s3"],
            "description": "AWS hybrid cloud architecture connecting on-premises to AWS"
        },
        "aws_event_driven": {
            "required_services": ["eventbridge", "lambda", "sqs"],
            "optional_services": ["sns", "kinesis", "step_functions"],
            "description": "AWS event-driven architecture using EventBridge and Lambda"
        },
        "aws_security_focused": {
            "required_services": ["waf", "shield", "iam"],
            "optional_services": ["guardduty", "security_hub", "kms"],
            "description": "AWS security-focused architecture with comprehensive protection layers"
        }
    }

    # Legacy architecture patterns for backward compatibility
    ARCHITECTURE_PATTERNS = {
        "microservices": {
            "components": ["api", "api", "api"],  # Multiple API services
            "description": "Microservices architecture with multiple independent services"
        },
        "three_tier": {
            "components": ["frontend", "backend", "database"],
            "description": "Three-tier architecture with frontend, backend, and database layers"
        },
        "event_driven": {
            "components": ["queue", "backend", "backend"],
            "description": "Event-driven architecture using message queues for communication"
        },
        "serverless": {
            "components": ["api", "serverless", "database"],
            "description": "Serverless architecture using functions for processing"
        },
        "cqrs": {
            "components": ["api", "backend", "queue", "database", "database"],
            "description": "Command Query Responsibility Segregation (CQRS) pattern"
        }
    }
    
    def __init__(self):
        """Initialize the architecture diagram analyzer."""
        pass

    def detect_aws_services(self, components: List[DiagramComponent]) -> Dict[str, List[str]]:
        """
        Detect AWS services from diagram components.

        Args:
            components: List of diagram components

        Returns:
            Dict[str, List[str]]: Mapping of AWS service types to component IDs
        """
        aws_services = {}

        for component in components:
            if not component.text:
                continue

            text = component.text.lower()
            detected_services = []

            # Check against AWS service keywords
            for service_name, keywords in self.AWS_SERVICE_KEYWORDS.items():
                for keyword in keywords:
                    if keyword.lower() in text:
                        detected_services.append(service_name)
                        break

            # Store detected services for this component
            if detected_services:
                for service in detected_services:
                    if service not in aws_services:
                        aws_services[service] = []
                    aws_services[service].append(component.component_id)

        return aws_services

    def classify_aws_component_roles(self, components: List[DiagramComponent]) -> Dict[str, Dict[str, Any]]:
        """
        Classify components with AWS service detection and confidence scoring.

        Args:
            components: List of diagram components

        Returns:
            Dict[str, Dict[str, Any]]: Mapping of component IDs to classification details
        """
        component_classifications = {}

        for component in components:
            classification = {
                "aws_services": [],
                "generic_role": "unknown",
                "confidence": 0.0,
                "properties": {}
            }

            if not component.text:
                component_classifications[component.component_id] = classification
                continue

            text = component.text.lower()
            aws_service_scores = {}
            generic_role_scores = {}

            # Check for AWS services
            for service_name, keywords in self.AWS_SERVICE_KEYWORDS.items():
                score = 0
                for keyword in keywords:
                    if keyword.lower() in text:
                        score += 1
                if score > 0:
                    aws_service_scores[service_name] = score

            # Check for generic roles (backward compatibility)
            for role_name, keywords in self.COMPONENT_KEYWORDS.items():
                score = 0
                for keyword in keywords:
                    if keyword.lower() in text:
                        score += 1
                if score > 0:
                    generic_role_scores[role_name] = score

            # Determine AWS services (can have multiple)
            if aws_service_scores:
                # Sort by score and take top services
                sorted_services = sorted(aws_service_scores.items(), key=lambda x: x[1], reverse=True)
                classification["aws_services"] = [service for service, score in sorted_services if score >= 1]
                classification["confidence"] = max(aws_service_scores.values()) / 3.0  # Normalize

            # Determine generic role (single best match)
            if generic_role_scores:
                best_role = max(generic_role_scores.items(), key=lambda x: x[1])
                classification["generic_role"] = best_role[0]
                if not classification["confidence"]:
                    classification["confidence"] = best_role[1] / 3.0  # Normalize

            # Add component type considerations
            if component.component_type == ComponentType.CIRCLE:
                if "database" in classification["aws_services"] or "rds" in classification["aws_services"]:
                    classification["confidence"] += 0.2
            elif component.component_type == ComponentType.BOX:
                if "ec2" in classification["aws_services"] or "lambda" in classification["aws_services"]:
                    classification["confidence"] += 0.2

            # Cap confidence at 1.0
            classification["confidence"] = min(classification["confidence"], 1.0)

            component_classifications[component.component_id] = classification

        return component_classifications
    
    def analyze_architecture(
        self,
        components: List[DiagramComponent],
        connections: List[DiagramConnection],
        labels: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Analyze an architecture diagram with enhanced AWS infrastructure analysis.

        Args:
            components: List of diagram components
            connections: List of connections between components
            labels: Dictionary of labels in the diagram

        Returns:
            Dict[str, Any]: Comprehensive analysis results
        """
        try:
            # AWS-specific analysis
            aws_services = self.detect_aws_services(components)
            component_classifications = self.classify_aws_component_roles(components)

            # Legacy component role classification for backward compatibility
            component_roles = self._classify_component_roles(components)

            # Build a graph representation of the architecture
            graph = self._build_architecture_graph(components, connections)

            # Identify AWS architecture patterns
            aws_patterns = self._identify_aws_architecture_patterns(aws_services, component_classifications)

            # Identify traditional architecture patterns
            patterns = self._identify_architecture_patterns(components, connections, component_roles, graph)

            # Analyze component relationships
            relationships = self._analyze_component_relationships(components, connections, graph)

            # AWS Well-Architected Framework assessment
            well_architected_assessment = self._assess_well_architected_framework(aws_services, component_classifications, connections)

            # Identify potential issues (enhanced with AWS-specific checks)
            issues = self._identify_potential_issues(components, connections, graph)
            aws_specific_issues = self._identify_aws_specific_issues(aws_services, component_classifications, connections)

            # Generate comprehensive summary
            summary = self._generate_aws_summary(components, connections, aws_services, aws_patterns, component_classifications)

            # Generate detailed analysis
            details = self._generate_aws_details(components, connections, aws_services, aws_patterns, relationships, well_architected_assessment)

            # Generate AWS-specific recommendations
            recommendations = self._generate_aws_recommendations(aws_services, component_classifications, aws_specific_issues, well_architected_assessment)

            return {
                "summary": summary,
                "details": details,
                "recommendations": recommendations,
                "aws_services": aws_services,
                "component_classifications": component_classifications,
                "aws_patterns": aws_patterns,
                "well_architected_assessment": well_architected_assessment,
                "component_roles": component_roles,  # Legacy support
                "patterns": [pattern.to_dict() for pattern in patterns],  # Legacy support
                "relationships": relationships,
                "issues": issues + aws_specific_issues
            }

        except Exception as e:
            logger.error(f"Error analyzing architecture diagram: {str(e)}")
            return {
                "summary": "Failed to analyze architecture diagram due to an error.",
                "details": [],
                "recommendations": [],
                "aws_services": {},
                "component_classifications": {},
                "aws_patterns": [],
                "well_architected_assessment": {}
            }
    
    def _classify_component_roles(self, components: List[DiagramComponent]) -> Dict[str, str]:
        """
        Classify components by their roles based on text and properties.
        
        Args:
            components: List of diagram components
            
        Returns:
            Dict[str, str]: Mapping of component IDs to roles
        """
        component_roles = {}
        
        for component in components:
            role = "unknown"
            confidence_scores = {}
            
            # Skip components without text
            if not component.text:
                component_roles[component.component_id] = role
                continue
            
            # Check component text against keywords
            text = component.text.lower()
            for role_name, keywords in self.COMPONENT_KEYWORDS.items():
                score = 0
                for keyword in keywords:
                    if keyword.lower() in text:
                        score += 1
                
                if score > 0:
                    confidence_scores[role_name] = score
            
            # Consider component type in classification
            if component.component_type == ComponentType.CIRCLE:
                # Circles are often databases or queues
                if "database" in confidence_scores:
                    confidence_scores["database"] += 0.5
                if "queue" in confidence_scores:
                    confidence_scores["queue"] += 0.5
            
            # Consider component properties
            if component.properties.get("is_database", False):
                confidence_scores["database"] = confidence_scores.get("database", 0) + 2
            
            # Assign the role with highest confidence
            if confidence_scores:
                role = max(confidence_scores.items(), key=lambda x: x[1])[0]
            
            component_roles[component.component_id] = role
        
        return component_roles

    def _identify_aws_architecture_patterns(
        self,
        aws_services: Dict[str, List[str]],
        component_classifications: Dict[str, Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Identify AWS-specific architecture patterns.

        Args:
            aws_services: Detected AWS services
            component_classifications: Component classifications with AWS services

        Returns:
            List[Dict[str, Any]]: Identified AWS architecture patterns
        """
        identified_patterns = []

        # Check each AWS pattern
        for pattern_name, pattern_config in self.AWS_ARCHITECTURE_PATTERNS.items():
            confidence = 0.0
            matched_services = []
            missing_services = []

            # Check required services
            required_services = pattern_config.get("required_services", [])
            for service in required_services:
                if service in aws_services:
                    matched_services.append(service)
                    confidence += 1.0
                else:
                    missing_services.append(service)

            # Check optional services (bonus points)
            optional_services = pattern_config.get("optional_services", [])
            for service in optional_services:
                if service in aws_services:
                    matched_services.append(service)
                    confidence += 0.5

            # Calculate confidence as percentage
            if required_services:
                base_confidence = len([s for s in required_services if s in aws_services]) / len(required_services)
                bonus_confidence = len([s for s in optional_services if s in aws_services]) * 0.1
                confidence = min(base_confidence + bonus_confidence, 1.0)

            # Only include patterns with reasonable confidence
            if confidence >= 0.6:  # At least 60% of required services present
                pattern = {
                    "name": pattern_name,
                    "confidence": confidence,
                    "description": pattern_config["description"],
                    "matched_services": matched_services,
                    "missing_services": missing_services,
                    "components": []
                }

                # Add component IDs for matched services
                for service in matched_services:
                    if service in aws_services:
                        pattern["components"].extend(aws_services[service])

                identified_patterns.append(pattern)

        # Sort by confidence
        identified_patterns.sort(key=lambda x: x["confidence"], reverse=True)

        return identified_patterns
    
    def _build_architecture_graph(
        self, components: List[DiagramComponent], connections: List[DiagramConnection]
    ) -> nx.DiGraph:
        """
        Build a directed graph representation of the architecture.
        
        Args:
            components: List of diagram components
            connections: List of connections between components
            
        Returns:
            nx.DiGraph: Directed graph of the architecture
        """
        graph = nx.DiGraph()
        
        # Add nodes (components)
        for component in components:
            graph.add_node(
                component.component_id,
                type=component.component_type,
                text=component.text,
                confidence=component.confidence
            )
        
        # Add edges (connections)
        for connection in connections:
            graph.add_edge(
                connection.source_id,
                connection.target_id,
                id=connection.connection_id,
                type=connection.connection_type,
                text=connection.text,
                confidence=connection.confidence
            )
        
        return graph
    
    def _identify_architecture_patterns(
        self,
        components: List[DiagramComponent],
        connections: List[DiagramConnection],
        component_roles: Dict[str, str],
        graph: nx.DiGraph
    ) -> List[ArchitecturePattern]:
        """
        Identify architecture patterns in the diagram.
        
        Args:
            components: List of diagram components
            connections: List of connections between components
            component_roles: Mapping of component IDs to roles
            graph: Directed graph of the architecture
            
        Returns:
            List[ArchitecturePattern]: Identified architecture patterns
        """
        patterns = []
        
        # Count component roles
        role_counts = Counter(component_roles.values())
        
        # Check for microservices pattern
        if role_counts.get("api", 0) >= 3:
            # Find API components
            api_components = [c.component_id for c in components if component_roles.get(c.component_id) == "api"]
            
            # Check if they are connected to each other or to a common component
            connected = False
            for api_id in api_components:
                if any(conn.source_id == api_id and conn.target_id in api_components for conn in connections) or \
                   any(conn.target_id == api_id and conn.source_id in api_components for conn in connections):
                    connected = True
                    break
            
            if connected or role_counts.get("api", 0) >= 5:  # If many APIs, assume microservices even without connections
                patterns.append(ArchitecturePattern(
                    name="Microservices Architecture",
                    confidence=0.8,
                    components=api_components,
                    description="Multiple independent services communicating with each other, "
                                "following the microservices architectural style."
                ))
        
        # Check for three-tier architecture
        if role_counts.get("frontend", 0) >= 1 and role_counts.get("backend", 0) >= 1 and role_counts.get("database", 0) >= 1:
            frontend_components = [c.component_id for c in components if component_roles.get(c.component_id) == "frontend"]
            backend_components = [c.component_id for c in components if component_roles.get(c.component_id) == "backend"]
            database_components = [c.component_id for c in components if component_roles.get(c.component_id) == "database"]
            
            # Check if they are connected in the right order
            frontend_to_backend = False
            backend_to_database = False
            
            for conn in connections:
                if conn.source_id in frontend_components and conn.target_id in backend_components:
                    frontend_to_backend = True
                if conn.source_id in backend_components and conn.target_id in database_components:
                    backend_to_database = True
            
            if frontend_to_backend and backend_to_database:
                pattern_components = frontend_components + backend_components + database_components
                patterns.append(ArchitecturePattern(
                    name="Three-Tier Architecture",
                    confidence=0.9,
                    components=pattern_components,
                    description="Classic three-tier architecture with separate frontend, "
                                "backend, and database layers."
                ))
        
        # Check for event-driven architecture
        if role_counts.get("queue", 0) >= 1 and role_counts.get("backend", 0) >= 2:
            queue_components = [c.component_id for c in components if component_roles.get(c.component_id) == "queue"]
            backend_components = [c.component_id for c in components if component_roles.get(c.component_id) == "backend"]
            
            # Check if backends are connected to queues
            backends_to_queue = 0
            queue_to_backends = 0
            
            for conn in connections:
                if conn.source_id in backend_components and conn.target_id in queue_components:
                    backends_to_queue += 1
                if conn.source_id in queue_components and conn.target_id in backend_components:
                    queue_to_backends += 1
            
            if backends_to_queue >= 1 and queue_to_backends >= 1:
                pattern_components = queue_components + backend_components
                patterns.append(ArchitecturePattern(
                    name="Event-Driven Architecture",
                    confidence=0.85,
                    components=pattern_components,
                    description="Event-driven architecture using message queues for communication "
                                "between services."
                ))
        
        # Check for serverless architecture
        if role_counts.get("serverless", 0) >= 1:
            serverless_components = [c.component_id for c in components if component_roles.get(c.component_id) == "serverless"]
            patterns.append(ArchitecturePattern(
                name="Serverless Architecture",
                confidence=0.8,
                components=serverless_components,
                description="Serverless architecture using functions for processing, "
                            "allowing for automatic scaling and reduced operational overhead."
            ))
        
        return patterns
    
    def _analyze_component_relationships(
        self,
        components: List[DiagramComponent],
        connections: List[DiagramConnection],
        graph: nx.DiGraph
    ) -> List[Dict[str, Any]]:
        """
        Analyze relationships between components.
        
        Args:
            components: List of diagram components
            connections: List of connections between components
            graph: Directed graph of the architecture
            
        Returns:
            List[Dict[str, Any]]: Analysis of component relationships
        """
        relationships = []
        
        # Identify central components (high degree centrality)
        if len(graph.nodes) > 0:
            centrality = nx.degree_centrality(graph)
            central_components = sorted(centrality.items(), key=lambda x: x[1], reverse=True)[:3]
            
            for component_id, score in central_components:
                if score > 0.2:  # Only consider components with significant centrality
                    component = next((c for c in components if c.component_id == component_id), None)
                    if component:
                        relationships.append({
                            "type": "central_component",
                            "component_id": component_id,
                            "component_text": component.text or "Unnamed component",
                            "score": score,
                            "description": f"Central component with connections to {int(score * (len(components) - 1))} other components"
                        })
        
        # Identify isolated components (no connections)
        for component in components:
            if graph.degree(component.component_id) == 0:
                relationships.append({
                    "type": "isolated_component",
                    "component_id": component.component_id,
                    "component_text": component.text or "Unnamed component",
                    "description": "Isolated component with no connections to other components"
                })
        
        # Identify bidirectional connections
        bidirectional_pairs = set()
        for conn1 in connections:
            for conn2 in connections:
                if conn1.source_id == conn2.target_id and conn1.target_id == conn2.source_id:
                    pair = tuple(sorted([conn1.source_id, conn1.target_id]))
                    if pair not in bidirectional_pairs:
                        bidirectional_pairs.add(pair)
                        
                        source_component = next((c for c in components if c.component_id == conn1.source_id), None)
                        target_component = next((c for c in components if c.component_id == conn1.target_id), None)
                        
                        if source_component and target_component:
                            relationships.append({
                                "type": "bidirectional_connection",
                                "components": [conn1.source_id, conn1.target_id],
                                "component_texts": [
                                    source_component.text or "Unnamed component",
                                    target_component.text or "Unnamed component"
                                ],
                                "description": f"Bidirectional communication between components"
                            })
        
        return relationships
    
    def _identify_potential_issues(
        self,
        components: List[DiagramComponent],
        connections: List[DiagramConnection],
        graph: nx.DiGraph
    ) -> List[Dict[str, Any]]:
        """
        Identify potential issues in the architecture.
        
        Args:
            components: List of diagram components
            connections: List of connections between components
            graph: Directed graph of the architecture
            
        Returns:
            List[Dict[str, Any]]: Potential issues in the architecture
        """
        issues = []
        
        # Check for single points of failure
        if len(graph.nodes) > 3:  # Only relevant for non-trivial architectures
            # Calculate betweenness centrality to find critical components
            try:
                betweenness = nx.betweenness_centrality(graph)
                critical_components = sorted(betweenness.items(), key=lambda x: x[1], reverse=True)[:2]
                
                for component_id, score in critical_components:
                    if score > 0.5:  # High betweenness indicates a potential bottleneck
                        component = next((c for c in components if c.component_id == component_id), None)
                        if component:
                            issues.append({
                                "type": "single_point_of_failure",
                                "component_id": component_id,
                                "component_text": component.text or "Unnamed component",
                                "severity": "high" if score > 0.7 else "medium",
                                "description": f"Potential single point of failure with high centrality score of {score:.2f}"
                            })
            except:
                # Betweenness centrality calculation may fail for some graph structures
                pass
        
        # Check for components with too many connections
        for component in components:
            degree = graph.degree(component.component_id)
            if degree > 5:  # Too many connections might indicate poor separation of concerns
                issues.append({
                    "type": "high_coupling",
                    "component_id": component.component_id,
                    "component_text": component.text or "Unnamed component",
                    "severity": "medium",
                    "description": f"High coupling with {degree} connections to other components"
                })
        
        # Check for isolated components
        for component in components:
            if graph.degree(component.component_id) == 0:
                issues.append({
                    "type": "isolated_component",
                    "component_id": component.component_id,
                    "component_text": component.text or "Unnamed component",
                    "severity": "low",
                    "description": "Isolated component with no connections to other components"
                })
        
        # Check for circular dependencies
        try:
            cycles = list(nx.simple_cycles(graph))
            if cycles:
                for cycle in cycles[:3]:  # Limit to first 3 cycles
                    cycle_components = []
                    for component_id in cycle:
                        component = next((c for c in components if c.component_id == component_id), None)
                        if component:
                            cycle_components.append(component.text or "Unnamed component")
                    
                    issues.append({
                        "type": "circular_dependency",
                        "components": cycle,
                        "component_texts": cycle_components,
                        "severity": "medium",
                        "description": f"Circular dependency detected between {', '.join(cycle_components)}"
                    })
        except:
            # Cycle detection may fail for some graph structures
            pass
        
        return issues
    
    def _generate_summary(
        self,
        components: List[DiagramComponent],
        connections: List[DiagramConnection],
        patterns: List[ArchitecturePattern],
        component_roles: Dict[str, str]
    ) -> str:
        """
        Generate a summary of the architecture diagram.
        
        Args:
            components: List of diagram components
            connections: List of connections between components
            patterns: List of identified architecture patterns
            component_roles: Mapping of component IDs to roles
            
        Returns:
            str: Summary of the architecture
        """
        # Count components by role
        role_counts = Counter(component_roles.values())
        
        # Start with basic component counts
        summary = f"Architecture diagram with {len(components)} components and {len(connections)} connections. "
        
        # Add information about component types
        role_descriptions = []
        for role, count in role_counts.items():
            if role != "unknown" and count > 0:
                role_descriptions.append(f"{count} {role.replace('_', ' ')}" + ("s" if count > 1 else ""))
        
        if role_descriptions:
            summary += f"Identified {', '.join(role_descriptions)}. "
        
        # Add information about architecture patterns
        if patterns:
            pattern_names = [pattern.name for pattern in patterns]
            summary += f"The architecture follows {', '.join(pattern_names)} patterns. "
        
        return summary.strip()
    
    def _generate_details(
        self,
        components: List[DiagramComponent],
        connections: List[DiagramConnection],
        patterns: List[ArchitecturePattern],
        relationships: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Generate detailed analysis of the architecture.
        
        Args:
            components: List of diagram components
            connections: List of connections between components
            patterns: List of identified architecture patterns
            relationships: Analysis of component relationships
            
        Returns:
            List[Dict[str, Any]]: Detailed analysis
        """
        details = []
        
        # Add pattern details
        for pattern in patterns:
            details.append({
                "type": "pattern",
                "content": f"{pattern.name}: {pattern.description}",
                "confidence": pattern.confidence
            })
        
        # Add relationship details
        for relationship in relationships[:5]:  # Limit to top 5 relationships
            if relationship["type"] == "central_component":
                details.append({
                    "type": "relationship",
                    "content": f"Central component: {relationship['component_text']} - {relationship['description']}",
                    "confidence": 0.8
                })
            elif relationship["type"] == "bidirectional_connection":
                details.append({
                    "type": "relationship",
                    "content": f"Bidirectional communication between {relationship['component_texts'][0]} and {relationship['component_texts'][1]}",
                    "confidence": 0.9
                })
        
        # Add component details for important components
        important_components = []
        for component in components:
            if component.text:  # Only consider components with text
                important_components.append((component, component.confidence))
        
        # Sort by confidence and take top 5
        important_components.sort(key=lambda x: x[1], reverse=True)
        for component, confidence in important_components[:5]:
            details.append({
                "type": "component",
                "content": f"{component.text} ({component.component_type})",
                "confidence": confidence
            })
        
        return details
    
    def _generate_recommendations(
        self,
        components: List[DiagramComponent],
        connections: List[DiagramConnection],
        issues: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Generate recommendations based on the architecture analysis.
        
        Args:
            components: List of diagram components
            connections: List of connections between components
            issues: List of identified issues
            
        Returns:
            List[Dict[str, Any]]: Recommendations
        """
        recommendations = []
        
        # Add recommendations based on issues
        for issue in issues:
            if issue["type"] == "single_point_of_failure":
                recommendations.append({
                    "type": "improvement",
                    "content": f"Consider adding redundancy for {issue['component_text']} to avoid a single point of failure.",
                    "priority": 1 if issue["severity"] == "high" else 2
                })
            elif issue["type"] == "high_coupling":
                recommendations.append({
                    "type": "improvement",
                    "content": f"Consider reducing the number of connections to {issue['component_text']} to improve modularity.",
                    "priority": 3
                })
            elif issue["type"] == "circular_dependency":
                recommendations.append({
                    "type": "improvement",
                    "content": f"Resolve circular dependency between {', '.join(issue['component_texts'])} to improve maintainability.",
                    "priority": 2
                })
        
        # Add general recommendations
        unlabeled_components = sum(1 for c in components if not c.text)
        if unlabeled_components > 0:
            recommendations.append({
                "type": "documentation",
                "content": f"Add labels to {unlabeled_components} unlabeled components to improve diagram clarity.",
                "priority": 4
            })
        
        # Add recommendation for connection labels if many connections lack text
        unlabeled_connections = sum(1 for c in connections if not c.text)
        if unlabeled_connections > 3 and len(connections) > 5:
            recommendations.append({
                "type": "documentation",
                "content": f"Add labels to connections to clarify the relationships between components.",
                "priority": 4
            })
        
        return recommendations
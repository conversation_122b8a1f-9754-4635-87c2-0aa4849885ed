# gemini_utils.py - Direct integration with Google Gemini API for vision models
import os
import json
import base64
import requests
from typing import Dict, Any, Optional, List
import logging
from PIL import Image
from io import BytesIO
from dotenv import load_dotenv
import re

# Load environment variables from the parent directory
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))

logger = logging.getLogger(__name__)

def detect_image_type_from_text(text_content: str) -> str:
    """
    Detect if an image is likely an architecture diagram or error screenshot based on extracted text.

    Args:
        text_content: Text extracted from the image via OCR

    Returns:
        str: 'architecture', 'error', or 'general'
    """
    if not text_content:
        return 'general'

    text_lower = text_content.lower()

    # Error indicators
    error_keywords = [
        'error', 'exception', 'failed', 'failure', 'traceback', 'stack trace',
        'fatal', 'critical', 'warning', 'stderr', 'errno', 'exit code',
        'syntax error', 'runtime error', 'null pointer', 'segmentation fault',
        'access denied', 'permission denied', 'connection refused', 'timeout',
        'not found', '404', '500', '503', 'internal server error'
    ]

    # Architecture diagram indicators
    architecture_keywords = [
        'api', 'database', 'server', 'client', 'service', 'microservice',
        'load balancer', 'cache', 'queue', 'storage', 'cdn', 'gateway',
        'aws', 'azure', 'gcp', 'cloud', 'kubernetes', 'docker', 'container',
        'vpc', 'subnet', 'firewall', 'proxy', 'nginx', 'apache', 'redis',
        'mongodb', 'mysql', 'postgresql', 'elasticsearch', 'kafka', 'rabbitmq'
    ]

    # Count matches
    error_score = sum(1 for keyword in error_keywords if keyword in text_lower)
    architecture_score = sum(1 for keyword in architecture_keywords if keyword in text_lower)

    # Determine type based on scores
    if error_score > architecture_score and error_score > 0:
        return 'error'
    elif architecture_score > 0:
        return 'architecture'
    else:
        return 'general'

def select_analysis_prompt(image_type: str, custom_prompt: Optional[str] = None) -> str:
    """
    Select the appropriate analysis prompt based on image type.

    Args:
        image_type: Type of image ('architecture', 'error', or 'general')
        custom_prompt: Optional custom prompt to use instead

    Returns:
        str: The prompt to use for analysis
    """
    if custom_prompt:
        return custom_prompt

    if image_type == 'architecture':
        return ARCHITECTURE_DIAGRAM_PROMPT
    elif image_type == 'error':
        return ERROR_SCREENSHOT_PROMPT
    else:
        return GENERAL_ANALYSIS_PROMPT

# Enhanced prompts for comprehensive image analysis
ARCHITECTURE_DIAGRAM_PROMPT = """
You are an expert system architect analyzing an architecture diagram. Provide a comprehensive and detailed analysis with the following structure:

## COMPONENT ANALYSIS
- Identify and describe each component, service, or system shown in the diagram
- Explain the purpose and function of each component
- Identify the technology stack if recognizable from visual elements

## ARCHITECTURE PATTERNS & RELATIONSHIPS
- Explain the relationships and data flow between components
- Analyze the architectural patterns being used (e.g., microservices, layered architecture, event-driven, serverless)
- Describe the overall system design approach

## TECHNICAL ASSESSMENT
- Identify potential bottlenecks or performance concerns
- Point out single points of failure
- Assess scalability considerations
- Evaluate security aspects visible in the design

## RECOMMENDATIONS & BEST PRACTICES
- Suggest improvements or optimizations
- Recommend best practices where applicable
- Identify missing components or considerations
- Propose alternative approaches if beneficial

## TECHNOLOGY INSIGHTS
- Identify specific technologies, frameworks, or cloud services if visible
- Comment on technology choices and their implications
- Suggest complementary technologies or tools

Provide specific, actionable insights that a developer or architect can use to understand and improve the system.
"""

ERROR_SCREENSHOT_PROMPT = """
You are an expert software troubleshooting specialist analyzing an error screenshot. Provide a comprehensive analysis with the following structure:

## ERROR IDENTIFICATION
- Extract and analyze the complete error message, including error codes and stack traces
- Identify the specific technology, framework, or programming language involved
- Determine the error type and category (syntax, runtime, configuration, network, etc.)

## ERROR EXPLANATION
- Explain what the error means in plain language
- Describe why this error typically occurs
- Identify the most likely root causes
- Explain the impact and severity of this error

## TROUBLESHOOTING STEPS
- Provide step-by-step troubleshooting suggestions
- List diagnostic commands or tools to use
- Suggest specific areas to investigate
- Recommend logging or monitoring to implement

## SOLUTION RECOMMENDATIONS
- Provide specific code fixes or configuration changes where possible
- Suggest multiple solution approaches (quick fixes vs. long-term solutions)
- Include preventive measures to avoid this error in the future
- Recommend relevant documentation or resources

## TECHNICAL CONTEXT
- Identify related technologies or dependencies that might be involved
- Suggest environment or configuration checks
- Recommend testing strategies to verify fixes

Focus on providing actionable, specific guidance that a developer can immediately implement to resolve the issue.
"""

GENERAL_ANALYSIS_PROMPT = """
Analyze this image comprehensively. If it appears to be an architecture diagram, focus on components, relationships, and technical design. If it appears to be an error screenshot, focus on error identification and troubleshooting. If it's neither, provide a detailed description of what you observe and any technical insights you can derive.

Structure your response with clear sections and provide specific, actionable information where possible.
"""

def format_analysis_response(raw_analysis: str, image_type: str, extracted_text: str = "") -> Dict[str, Any]:
    """
    Format the raw Gemini analysis response into a structured format.

    Args:
        raw_analysis: Raw analysis text from Gemini
        image_type: Type of image analyzed
        extracted_text: Text extracted from the image via OCR

    Returns:
        Dict[str, Any]: Structured analysis response
    """
    # Split analysis into sections
    sections = _parse_analysis_sections(raw_analysis)

    # Extract key components based on image type
    if image_type == 'architecture':
        return _format_architecture_response(sections, raw_analysis, extracted_text)
    elif image_type == 'error':
        return _format_error_response(sections, raw_analysis, extracted_text)
    else:
        return _format_general_response(sections, raw_analysis, extracted_text)

def _parse_analysis_sections(analysis: str) -> Dict[str, str]:
    """Parse analysis text into sections based on headers."""
    sections = {}
    current_section = "summary"
    current_content = []

    lines = analysis.split('\n')
    for line in lines:
        # Check if line is a header (starts with ## or #)
        if line.strip().startswith('##') or line.strip().startswith('#'):
            # Save previous section
            if current_content:
                sections[current_section] = '\n'.join(current_content).strip()

            # Start new section
            header = line.strip().replace('#', '').strip().lower()
            current_section = header.replace(' ', '_').replace('&', 'and')
            current_content = []
        else:
            current_content.append(line)

    # Save last section
    if current_content:
        sections[current_section] = '\n'.join(current_content).strip()

    return sections

def _format_architecture_response(sections: Dict[str, str], raw_analysis: str, extracted_text: str) -> Dict[str, Any]:
    """Format response for architecture diagrams."""
    # Extract components and technologies
    components = _extract_components(sections.get('component_analysis', ''))
    technologies = _extract_technologies(raw_analysis)
    patterns = _extract_patterns(sections.get('architecture_patterns_and_relationships', ''))

    return {
        "analysis_type": "architecture_diagram",
        "summary": _generate_architecture_summary(sections, components, technologies),
        "components": components,
        "architecture_patterns": patterns,
        "technology_stack": technologies,
        "relationships": _extract_relationships(sections.get('architecture_patterns_and_relationships', '')),
        "technical_assessment": {
            "bottlenecks": _extract_bottlenecks(sections.get('technical_assessment', '')),
            "single_points_of_failure": _extract_spof(sections.get('technical_assessment', '')),
            "scalability_concerns": _extract_scalability(sections.get('technical_assessment', '')),
            "security_considerations": _extract_security(sections.get('technical_assessment', ''))
        },
        "recommendations": _extract_recommendations(sections.get('recommendations_and_best_practices', '')),
        "next_steps": _extract_next_steps(sections.get('recommendations_and_best_practices', '')),
        "extracted_text": extracted_text,
        "full_analysis": raw_analysis
    }

def _format_error_response(sections: Dict[str, str], raw_analysis: str, extracted_text: str) -> Dict[str, Any]:
    """Format response for error screenshots."""
    error_details = _extract_error_details(sections.get('error_identification', ''))

    return {
        "analysis_type": "error_screenshot",
        "summary": _generate_error_summary(sections, error_details),
        "error_details": error_details,
        "root_causes": _extract_root_causes(sections.get('error_explanation', '')),
        "troubleshooting_steps": _extract_troubleshooting_steps(sections.get('troubleshooting_steps', '')),
        "solutions": _extract_solutions(sections.get('solution_recommendations', '')),
        "prevention_measures": _extract_prevention(sections.get('solution_recommendations', '')),
        "related_technologies": _extract_technologies(raw_analysis),
        "severity_assessment": _assess_error_severity(error_details, raw_analysis),
        "extracted_text": extracted_text,
        "full_analysis": raw_analysis
    }

def _format_general_response(sections: Dict[str, str], raw_analysis: str, extracted_text: str) -> Dict[str, Any]:
    """Format response for general images."""
    return {
        "analysis_type": "general",
        "summary": raw_analysis[:500] + "..." if len(raw_analysis) > 500 else raw_analysis,
        "key_observations": _extract_key_points(raw_analysis),
        "technical_insights": _extract_technologies(raw_analysis),
        "extracted_text": extracted_text,
        "full_analysis": raw_analysis
    }

# Helper functions for extracting specific information
def _extract_components(text: str) -> List[str]:
    """Extract component names from analysis text."""
    components = []
    lines = text.split('\n')
    for line in lines:
        # Look for bullet points or numbered lists
        if re.match(r'^[-*•]\s+', line.strip()) or re.match(r'^\d+\.\s+', line.strip()):
            # Extract component name (first few words)
            content = re.sub(r'^[-*•\d\.\s]+', '', line.strip())
            if content:
                # Take first part before colon or dash
                component = content.split(':')[0].split('-')[0].strip()
                if len(component) > 3 and len(component) < 50:
                    components.append(component)
    return components[:10]  # Limit to top 10

def _extract_technologies(text: str) -> List[str]:
    """Extract technology names from analysis text."""
    tech_patterns = [
        r'\b(AWS|Azure|GCP|Google Cloud)\b',
        r'\b(Docker|Kubernetes|K8s)\b',
        r'\b(React|Angular|Vue|Node\.js|Express)\b',
        r'\b(MySQL|PostgreSQL|MongoDB|Redis|Elasticsearch)\b',
        r'\b(Nginx|Apache|HAProxy|Cloudflare)\b',
        r'\b(Jenkins|GitLab|GitHub|CircleCI)\b',
        r'\b(Terraform|Ansible|Chef|Puppet)\b',
        r'\b(Java|Python|JavaScript|TypeScript|Go|Rust|C\#)\b'
    ]

    technologies = set()
    for pattern in tech_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        technologies.update(matches)

    return list(technologies)[:15]  # Limit to top 15

def _extract_patterns(text: str) -> List[str]:
    """Extract architecture patterns from analysis text."""
    pattern_keywords = [
        'microservices', 'monolith', 'serverless', 'event-driven',
        'layered architecture', 'mvc', 'mvp', 'mvvm', 'clean architecture',
        'hexagonal architecture', 'onion architecture', 'cqrs', 'event sourcing',
        'pub/sub', 'message queue', 'load balancing', 'circuit breaker'
    ]

    patterns = []
    text_lower = text.lower()
    for pattern in pattern_keywords:
        if pattern in text_lower:
            patterns.append(pattern.title())

    return patterns

def _extract_relationships(text: str) -> List[str]:
    """Extract component relationships from analysis text."""
    relationships = []
    lines = text.split('\n')
    for line in lines:
        if any(word in line.lower() for word in ['connects', 'communicates', 'sends', 'receives', 'flows']):
            clean_line = line.strip()
            if len(clean_line) > 10 and len(clean_line) < 200:
                relationships.append(clean_line)
    return relationships[:8]  # Limit to top 8

def _extract_bottlenecks(text: str) -> List[str]:
    """Extract potential bottlenecks from analysis text."""
    bottleneck_indicators = ['bottleneck', 'performance', 'slow', 'latency', 'throughput', 'capacity']
    return _extract_by_keywords(text, bottleneck_indicators, 'bottleneck')

def _extract_spof(text: str) -> List[str]:
    """Extract single points of failure from analysis text."""
    spof_indicators = ['single point', 'failure', 'critical', 'dependency', 'unavailable']
    return _extract_by_keywords(text, spof_indicators, 'failure')

def _extract_scalability(text: str) -> List[str]:
    """Extract scalability concerns from analysis text."""
    scalability_indicators = ['scalability', 'scale', 'horizontal', 'vertical', 'elastic', 'auto-scaling']
    return _extract_by_keywords(text, scalability_indicators, 'scalability')

def _extract_security(text: str) -> List[str]:
    """Extract security considerations from analysis text."""
    security_indicators = ['security', 'authentication', 'authorization', 'encryption', 'firewall', 'vulnerability']
    return _extract_by_keywords(text, security_indicators, 'security')

def _extract_by_keywords(text: str, keywords: List[str], context: str) -> List[str]:
    """Extract sentences containing specific keywords."""
    results = []
    sentences = text.split('.')
    for sentence in sentences:
        if any(keyword in sentence.lower() for keyword in keywords):
            clean_sentence = sentence.strip()
            if len(clean_sentence) > 15 and len(clean_sentence) < 300:
                results.append(clean_sentence)
    return results[:5]  # Limit to top 5

def _extract_recommendations(text: str) -> List[str]:
    """Extract recommendations from analysis text."""
    recommendations = []
    lines = text.split('\n')
    for line in lines:
        if re.match(r'^[-*•]\s+', line.strip()) or re.match(r'^\d+\.\s+', line.strip()):
            content = re.sub(r'^[-*•\d\.\s]+', '', line.strip())
            if content and any(word in content.lower() for word in ['recommend', 'suggest', 'should', 'consider', 'improve']):
                recommendations.append(content)
    return recommendations[:8]  # Limit to top 8

def _extract_next_steps(text: str) -> List[str]:
    """Extract next steps from analysis text."""
    next_steps = []
    lines = text.split('\n')
    for line in lines:
        if any(phrase in line.lower() for phrase in ['next step', 'action item', 'todo', 'implement', 'deploy']):
            clean_line = line.strip()
            if len(clean_line) > 10 and len(clean_line) < 200:
                next_steps.append(clean_line)
    return next_steps[:5]  # Limit to top 5

def _extract_error_details(text: str) -> Dict[str, Any]:
    """Extract error details from analysis text."""
    error_details = {
        "error_type": "Unknown",
        "error_message": "",
        "technology": "Unknown",
        "severity": "Medium"
    }

    # Extract error type
    error_types = ['syntax error', 'runtime error', 'compilation error', 'network error', 'database error', 'authentication error']
    for error_type in error_types:
        if error_type in text.lower():
            error_details["error_type"] = error_type.title()
            break

    # Extract technology
    technologies = ['java', 'python', 'javascript', 'c#', 'php', 'ruby', 'go', 'rust', 'sql', 'html', 'css']
    for tech in technologies:
        if tech in text.lower():
            error_details["technology"] = tech.title()
            break

    # Extract severity
    if any(word in text.lower() for word in ['critical', 'fatal', 'severe']):
        error_details["severity"] = "High"
    elif any(word in text.lower() for word in ['warning', 'minor', 'low']):
        error_details["severity"] = "Low"

    return error_details

def _extract_root_causes(text: str) -> List[str]:
    """Extract root causes from error analysis."""
    causes = []
    lines = text.split('\n')
    for line in lines:
        if any(phrase in line.lower() for phrase in ['cause', 'reason', 'due to', 'because', 'result of']):
            clean_line = line.strip()
            if len(clean_line) > 15 and len(clean_line) < 250:
                causes.append(clean_line)
    return causes[:6]  # Limit to top 6

def _extract_troubleshooting_steps(text: str) -> List[str]:
    """Extract troubleshooting steps from analysis."""
    steps = []
    lines = text.split('\n')
    for line in lines:
        if re.match(r'^\d+\.\s+', line.strip()) or any(word in line.lower() for word in ['check', 'verify', 'test', 'debug', 'examine']):
            content = re.sub(r'^\d+\.\s+', '', line.strip())
            if content and len(content) > 10 and len(content) < 300:
                steps.append(content)
    return steps[:10]  # Limit to top 10

def _extract_solutions(text: str) -> List[str]:
    """Extract solutions from error analysis."""
    solutions = []
    lines = text.split('\n')
    for line in lines:
        if any(word in line.lower() for word in ['solution', 'fix', 'resolve', 'correct', 'update', 'change']):
            clean_line = line.strip()
            if len(clean_line) > 15 and len(clean_line) < 300:
                solutions.append(clean_line)
    return solutions[:8]  # Limit to top 8

def _extract_prevention(text: str) -> List[str]:
    """Extract prevention measures from analysis."""
    prevention = []
    lines = text.split('\n')
    for line in lines:
        if any(word in line.lower() for word in ['prevent', 'avoid', 'monitor', 'alert', 'best practice']):
            clean_line = line.strip()
            if len(clean_line) > 15 and len(clean_line) < 250:
                prevention.append(clean_line)
    return prevention[:6]  # Limit to top 6

def _assess_error_severity(error_details: Dict[str, Any], full_text: str) -> str:
    """Assess error severity based on details and context."""
    severity_indicators = {
        "Critical": ['fatal', 'critical', 'crash', 'system down', 'data loss', 'security breach'],
        "High": ['error', 'exception', 'failed', 'broken', 'unavailable'],
        "Medium": ['warning', 'deprecated', 'slow', 'timeout'],
        "Low": ['info', 'debug', 'minor', 'cosmetic']
    }

    text_lower = full_text.lower()
    for severity, indicators in severity_indicators.items():
        if any(indicator in text_lower for indicator in indicators):
            return severity

    return error_details.get("severity", "Medium")

def _extract_key_points(text: str) -> List[str]:
    """Extract key points from general analysis."""
    key_points = []
    sentences = text.split('.')
    for sentence in sentences:
        # Look for sentences with technical terms or important information
        if any(word in sentence.lower() for word in ['important', 'key', 'main', 'primary', 'significant', 'notable']):
            clean_sentence = sentence.strip()
            if len(clean_sentence) > 20 and len(clean_sentence) < 200:
                key_points.append(clean_sentence)
    return key_points[:8]  # Limit to top 8

def _generate_architecture_summary(sections: Dict[str, str], components: List[str], technologies: List[str]) -> str:
    """Generate a summary for architecture analysis."""
    summary_parts = []

    if components:
        summary_parts.append(f"This architecture diagram shows {len(components)} main components")
        if len(components) <= 3:
            summary_parts.append(f"including {', '.join(components[:3])}")

    if technologies:
        summary_parts.append(f"The technology stack includes {', '.join(technologies[:5])}")

    # Add pattern information if available
    patterns_text = sections.get('architecture_patterns_and_relationships', '')
    if 'microservice' in patterns_text.lower():
        summary_parts.append("following a microservices architecture pattern")
    elif 'monolith' in patterns_text.lower():
        summary_parts.append("using a monolithic architecture approach")

    if summary_parts:
        return '. '.join(summary_parts) + '.'
    else:
        return "This appears to be an architecture diagram showing system components and their relationships."

def _generate_error_summary(sections: Dict[str, str], error_details: Dict[str, Any]) -> str:
    """Generate a summary for error analysis."""
    error_type = error_details.get("error_type", "Unknown")
    technology = error_details.get("technology", "Unknown")
    severity = error_details.get("severity", "Medium")

    summary = f"This appears to be a {severity.lower()} severity {error_type.lower()}"

    if technology != "Unknown":
        summary += f" in {technology}"

    # Add context from error identification section
    error_id_text = sections.get('error_identification', '')
    if error_id_text:
        # Extract first meaningful sentence
        sentences = error_id_text.split('.')
        for sentence in sentences:
            if len(sentence.strip()) > 20:
                summary += f". {sentence.strip()}"
                break

    return summary

def analyze_image_with_gemini(image_bytes: bytes, prompt: str = None, enhanced_analysis: bool = True, extracted_text: str = "") -> Dict[str, Any]:
    """
    Analyze image using Google Gemini API directly.

    Args:
        image_bytes: Image file as bytes
        prompt: Optional custom prompt to use for analysis
        enhanced_analysis: Whether to use enhanced structured analysis (default: True)
        extracted_text: Text extracted from image via OCR for better prompt selection

    Returns:
        Dictionary containing analysis results
    """
    try:
        # Get API key from environment variable
        api_key = os.getenv('GOOGLE_GEMINI_API_KEY')
        if not api_key:
            return {
                "status": "error",
                "error": "GOOGLE_GEMINI_API_KEY environment variable not set",
                "analysis": "Failed to analyze image with Google Gemini API."
            }
        
        # Check image size (API may have limits)
        if len(image_bytes) > 20 * 1024 * 1024:  # 20MB limit
            return {
                "status": "error",
                "error": f"Image size ({len(image_bytes) / (1024 * 1024):.2f}MB) exceeds the 20MB limit",
                "analysis": "Image is too large for processing. Please use a smaller image."
            }
            
        # Resize image if it's larger than 5MB to optimize processing
        if len(image_bytes) > 5 * 1024 * 1024:
            try:
                # Open image and resize while maintaining aspect ratio
                with Image.open(BytesIO(image_bytes)) as img:
                    # Calculate new dimensions (maintaining aspect ratio)
                    max_size = 1600  # Maximum dimension in pixels
                    ratio = min(max_size / img.width, max_size / img.height)
                    new_size = (int(img.width * ratio), int(img.height * ratio))
                    
                    # Resize image
                    img = img.resize(new_size, Image.LANCZOS)
                    
                    # Save to BytesIO
                    buffer = BytesIO()
                    img.save(buffer, format="JPEG", quality=85)
                    image_bytes = buffer.getvalue()
                    
                    logger.info(f"Resized image to {len(image_bytes) / (1024 * 1024):.2f}MB")
            except Exception as e:
                logger.warning(f"Failed to resize large image: {e}")
                # Continue with original image
        
        # Convert image to base64
        base64_image = base64.b64encode(image_bytes).decode('utf-8')
        
        # Determine image format
        image_format = "image/jpeg"
        try:
            with Image.open(BytesIO(image_bytes)) as img:
                if img.format:
                    image_format = f"image/{img.format.lower()}"
        except:
            pass  # Default to jpeg
            
        # Select appropriate prompt based on image type and enhancement setting
        if enhanced_analysis and not prompt:
            # Detect image type from extracted text
            image_type = detect_image_type_from_text(extracted_text)
            prompt = select_analysis_prompt(image_type, prompt)
            logger.info(f"Using enhanced analysis for image type: {image_type}")
        elif not prompt:
            # Use basic prompt for backward compatibility
            prompt = "Please analyze this image in detail. If it's an architecture diagram, explain the components and their relationships. If it's a screenshot containing an error, explain what the error is and suggest possible solutions. Provide a comprehensive analysis of what you see in the image."
        
        # Get model ID from environment variable
        model_id = os.getenv('GOOGLE_GEMINI_MODEL_ID', 'gemini-2.0-flash-exp')
        
        # Prepare the request payload for Gemini API
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        },
                        {
                            "inline_data": {
                                "mime_type": image_format,
                                "data": base64_image
                            }
                        }
                    ]
                }
            ],
            "generationConfig": {
                "temperature": 0.2,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 2048
            },
            "safetySettings": [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        }
        
        # Set up headers
        headers = {
            "Content-Type": "application/json"
        }
        
        # Construct the API URL
        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_id}:generateContent?key={api_key}"
        
        # Make the API request
        logger.info(f"Sending request to Google Gemini API with model: {model_id}")
        response = requests.post(
            url=api_url,
            headers=headers,
            data=json.dumps(payload),
            timeout=90  # Increase timeout for image processing
        )
        
        # Log the response status for debugging
        logger.info(f"Google Gemini API response status: {response.status_code}")
        
        # Check if the request was successful
        if response.status_code == 200:
            try:
                response_data = response.json()
                logger.info(f"Google Gemini API response data keys: {response_data.keys()}")
                
                # Extract the analysis text from the response
                if 'candidates' in response_data and len(response_data['candidates']) > 0:
                    candidate = response_data['candidates'][0]
                    logger.info(f"Candidate structure: {candidate.keys()}")

                    if 'content' in candidate and 'parts' in candidate['content']:
                        parts = candidate['content']['parts']
                        logger.info(f"Parts structure: {parts}")

                        if len(parts) > 0 and 'text' in parts[0]:
                            analysis = parts[0]['text']
                            logger.info(f"Extracted analysis length: {len(analysis)}")

                            # Format response based on enhancement setting
                            if enhanced_analysis:
                                image_type = detect_image_type_from_text(extracted_text)
                                formatted_response = format_analysis_response(analysis, image_type, extracted_text)

                                return {
                                    "status": "success",
                                    "analysis": analysis,  # Keep raw analysis for backward compatibility
                                    "enhanced_analysis": formatted_response,  # Add structured analysis
                                    "model_id": model_id,
                                    "image_type": image_type
                                }
                            else:
                                return {
                                    "status": "success",
                                    "analysis": analysis,
                                    "model_id": model_id
                                }
                        else:
                            logger.error(f"No text in response parts: {parts}")
                            return {
                                "status": "error",
                                "error": "No text content in Gemini API response",
                                "raw_response": str(response_data),
                                "analysis": "Failed to analyze image with Google Gemini API due to unexpected response format."
                            }
                    else:
                        logger.error(f"Unexpected candidate structure: {candidate}")
                        return {
                            "status": "error",
                            "error": "Unexpected response structure from Google Gemini API",
                            "raw_response": str(response_data),
                            "analysis": "Failed to analyze image with Google Gemini API due to unexpected response format."
                        }
                else:
                    logger.error(f"No candidates in response: {response_data}")
                    return {
                        "status": "error",
                        "error": "No content in response from Google Gemini API",
                        "raw_response": str(response_data),
                        "analysis": "Failed to analyze image with Google Gemini API."
                    }
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                return {
                    "status": "error",
                    "error": f"Failed to parse JSON response: {e}",
                    "raw_response": response.text[:1000],  # Include part of the raw response for debugging
                    "analysis": "Failed to analyze image with Google Gemini API due to invalid JSON response."
                }
        elif response.status_code == 429:
            return {
                "status": "error",
                "error": "Rate limit exceeded on Google Gemini API",
                "analysis": "The Google Gemini API rate limit has been exceeded. Please try again later."
            }
        elif response.status_code == 401:
            return {
                "status": "error",
                "error": "Authentication failed with Google Gemini API",
                "analysis": "The Google Gemini API key is invalid or has expired."
            }
        elif response.status_code == 400:
            try:
                error_data = response.json()
                error_message = error_data.get('error', {}).get('message', 'Bad request')
                return {
                    "status": "error",
                    "error": f"Bad request to Google Gemini API: {error_message}",
                    "analysis": "Failed to analyze image with Google Gemini API due to bad request."
                }
            except:
                return {
                    "status": "error",
                    "error": f"Bad request to Google Gemini API: {response.text}",
                    "analysis": "Failed to analyze image with Google Gemini API due to bad request."
                }
        else:
            error_message = f"Google Gemini API request failed with status code {response.status_code}: {response.text}"
            logger.error(error_message)
            return {
                "status": "error",
                "error": error_message,
                "analysis": "Failed to analyze image with Google Gemini API."
            }
    except Exception as e:
        logger.error(f"Image analysis with Google Gemini failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "analysis": "Failed to analyze image with Google Gemini API."
        }

def test_gemini_api_connection() -> Dict[str, Any]:
    """
    Test the Google Gemini API connection with a simple text request.
    
    Returns:
        Dictionary containing test results
    """
    try:
        api_key = os.getenv('GOOGLE_GEMINI_API_KEY')
        if not api_key:
            return {
                "status": "error",
                "error": "GOOGLE_GEMINI_API_KEY environment variable not set"
            }
        
        model_id = os.getenv('GOOGLE_GEMINI_MODEL_ID', 'gemini-2.0-flash-exp')
        
        # Simple test payload
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": "Hello, can you respond with 'API connection successful'?"
                        }
                    ]
                }
            ]
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_id}:generateContent?key={api_key}"
        
        response = requests.post(
            url=api_url,
            headers=headers,
            data=json.dumps(payload),
            timeout=30
        )
        
        if response.status_code == 200:
            response_data = response.json()
            if 'candidates' in response_data and len(response_data['candidates']) > 0:
                return {
                    "status": "success",
                    "message": "Google Gemini API connection successful",
                    "model_id": model_id
                }
        
        return {
            "status": "error",
            "error": f"API test failed with status {response.status_code}: {response.text}"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": f"API test failed: {str(e)}"
        }

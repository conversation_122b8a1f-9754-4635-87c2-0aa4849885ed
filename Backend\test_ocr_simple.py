"""
Simple OCR functionality test to verify current state.
"""
import os
import tempfile
from PIL import Image, ImageDraw, ImageFont
from aws_utils import extract_text_from_image_bytes
from image_analysis.ocr import OCRService
import io
import pytesseract

# Configure Tesseract path for Windows
if os.name == 'nt':  # Windows
    tesseract_path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    if os.path.exists(tesseract_path):
        pytesseract.pytesseract.tesseract_cmd = tesseract_path
        print(f"✅ Configured Tesseract path: {tesseract_path}")
    else:
        print(f"❌ Tesseract not found at: {tesseract_path}")


def create_simple_test_image():
    """Create a simple test image with text."""
    # Create a white image with black text
    img = Image.new('RGB', (300, 100), color='white')
    draw = ImageDraw.Draw(img)
    
    # Use default font
    try:
        font = ImageFont.load_default()
    except:
        font = None
    
    # Draw text
    text = "Hello OCR Test"
    draw.text((50, 30), text, fill='black', font=font)
    
    return img


def test_basic_ocr():
    """Test basic OCR functionality."""
    print("=== Testing Basic OCR Functionality ===")
    
    # Create test image
    img = create_simple_test_image()
    
    # Convert to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    # Test extract_text_from_image_bytes function
    print("Testing extract_text_from_image_bytes...")
    try:
        text = extract_text_from_image_bytes(img_bytes.getvalue())
        print(f"Extracted text: '{text}'")
        if text.strip():
            print("✅ OCR extraction successful")
        else:
            print("⚠️ OCR extraction returned empty text")
    except Exception as e:
        print(f"❌ OCR extraction failed: {e}")
    
    # Test OCRService
    print("\nTesting OCRService...")
    try:
        ocr_service = OCRService()
        
        # Save image to temporary file
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            img.save(tmp_file.name)
            
            # Extract text
            text = ocr_service.extract_text(tmp_file.name)
            print(f"OCRService extracted text: '{text}'")
            
            if text.strip():
                print("✅ OCRService extraction successful")
            else:
                print("⚠️ OCRService extraction returned empty text")
            
            # Clean up
            os.unlink(tmp_file.name)
            
    except Exception as e:
        print(f"❌ OCRService failed: {e}")


def test_ocr_service_initialization():
    """Test OCR service initialization and configuration."""
    print("\n=== Testing OCR Service Initialization ===")
    
    try:
        ocr_service = OCRService()
        print("✅ OCRService initialized successfully")
        
        # Test configuration
        print(f"Default language: {ocr_service.default_language}")
        print(f"Default mode: {ocr_service.default_mode}")
        print(f"Default preprocessing: {ocr_service.default_preprocessing}")
        
    except Exception as e:
        print(f"❌ OCRService initialization failed: {e}")


def test_error_handling():
    """Test OCR error handling."""
    print("\n=== Testing Error Handling ===")
    
    try:
        ocr_service = OCRService()
        
        # Test with non-existent file
        text = ocr_service.extract_text("non_existent_file.png")
        print(f"Non-existent file result: '{text}'")
        
        # Test with SVG file
        with tempfile.NamedTemporaryFile(suffix='.svg', delete=False) as tmp_file:
            tmp_file.write(b'<svg></svg>')
            tmp_file.flush()
            
            text = ocr_service.extract_text(tmp_file.name)
            print(f"SVG file result: '{text}'")
            
            os.unlink(tmp_file.name)
        
        print("✅ Error handling working correctly")
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")


if __name__ == "__main__":
    test_ocr_service_initialization()
    test_basic_ocr()
    test_error_handling()
    
    print("\n=== Summary ===")
    print("OCR tests completed. Check output above for results.")
